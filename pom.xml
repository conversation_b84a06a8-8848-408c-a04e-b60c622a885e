<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>me.zhyd.oauth</groupId>
  <artifactId>JustAuth</artifactId>
  <version>1.16.7</version>

  <name>JustAuth</name>
  <url>https://gitee.com/yadong.zhang/JustAuth</url>
  <description>
    小而全而美的第三方登录开源组件。目前已支持Github、Gitee、微博、钉钉、百度、Coding、腾讯云开发者平台、OSChina、支付宝、QQ、微信、淘宝、Google、Facebook、抖音、领英、小米、微软、今日头条、Teambition、StackOverflow、Pinterest、人人、华为、企业微信、酷家乐、Gitlab、美团、饿了么、推特、飞书、京东、阿里云、喜马拉雅、Amazon、Slack和Line等第三方平台的授权登录。 Login, so easy!
  </description>

  <licenses>
    <license>
      <name>MIT</name>
      <url>https://gitee.com/yadong.zhang/JustAuth/blob/master/LICENSE</url>
    </license>
  </licenses>

  <scm>
    <connection>scm:git:https://gitee.com/yadong.zhang/JustAuth.git</connection>
    <developerConnection>scm:git:https://gitee.com/yadong.zhang/JustAuth.git</developerConnection>
    <url>https://gitee.com/yadong.zhang/JustAuth</url>
  </scm>

  <developers>
    <developer>
      <name>Yadong.Zhang</name>
      <email><EMAIL></email>
      <url>https://www.zhyd.me</url>
    </developer>
    <developer>
      <name>Yangkai.Shen</name>
      <email><EMAIL></email>
      <url>https://xkcoding.com</url>
    </developer>
    <developer>
      <name>Hongwei.Peng</name>
      <email><EMAIL></email>
      <url>https://github.com/pengisgood</url>
    </developer>
  </developers>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <java.version>1.8</java.version>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <maven-source.version>2.2.1</maven-source.version>
    <maven-compiler.version>3.8.1</maven-compiler.version>
    <maven-javadoc.version>2.9.1</maven-javadoc.version>
    <cobertura-version>2.7</cobertura-version>
    <maven-surefire-version>2.20</maven-surefire-version>
    <maven-gpg-version>1.6</maven-gpg-version>
    <maven.test.skip>false</maven.test.skip>
    <simple-http.version>1.0.5</simple-http.version>
    <lombok-version>1.18.30</lombok-version>
    <junit-version>4.13.2</junit-version>
    <fastjson-version>1.2.83</fastjson-version>
    <alipay-sdk-version>4.39.165.ALL</alipay-sdk-version>
    <jacoco-version>0.8.2</jacoco-version>
    <jwt.version>0.12.3</jwt.version>
    <bcpkix-jdk18on.version>1.78</bcpkix-jdk18on.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok-version}</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.xkcoding.http</groupId>
      <artifactId>simple-http</artifactId>
      <version>${simple-http.version}</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit-version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>${fastjson-version}</version>
    </dependency>
    <dependency>
      <groupId>com.alipay.sdk</groupId>
      <artifactId>alipay-sdk-java</artifactId>
      <version>${alipay-sdk-version}</version>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <artifactId>fastjson</artifactId>
          <groupId>com.alibaba</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-api</artifactId>
      <version>${jwt.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-impl</artifactId>
      <version>${jwt.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-jackson</artifactId>
      <version>${jwt.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk18on</artifactId>
      <version>${bcpkix-jdk18on.version}</version>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <finalName>${project.artifactId}-${project.version}</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler.version}</version>
        <configuration>
          <encoding>${project.build.sourceEncoding}</encoding>
          <source>${java.version}</source>
          <target>${java.version}</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>${maven-source.version}</version>
        <inherited>true</inherited>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${maven-javadoc.version}</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
            <additionalparam>${javadoc.opts}</additionalparam>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven-surefire-version}</version>
        <configuration>
          <!-- 包含其他测试类 -->
          <includes>
            <include>**/*Test.java</include>
          </includes>
          <!-- 排除掉AuthRequestTest测试类，该类只做api演示用 -->
          <excludes>
            <exclude>**/AuthRequestTest.java</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>snapshot</id>
      <distributionManagement>
        <snapshotRepository>
          <id>ossrh</id>
          <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
    <profile>
      <id>release</id>
      <build>
        <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-source-plugin</artifactId>
              <version>${maven-source.version}</version>
              <inherited>true</inherited>
              <executions>
                <execution>
                  <phase>package</phase>
                  <goals>
                    <goal>jar-no-fork</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
            <plugin>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>cobertura-maven-plugin</artifactId>
              <version>${cobertura-version}</version>
              <configuration>
                <formats>
                  <format>html</format>
                  <format>xml</format>
                </formats>
                <check/>
              </configuration>
            </plugin>
            <plugin>
              <groupId>org.jacoco</groupId>
              <artifactId>jacoco-maven-plugin</artifactId>
              <version>${jacoco-version}</version>
              <executions>
                <execution>
                  <goals>
                    <goal>prepare-agent</goal>
                  </goals>
                </execution>
                <execution>
                  <id>report</id>
                  <phase>test</phase>
                  <goals>
                    <goal>report</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-gpg-plugin</artifactId>
              <version>${maven-gpg-version}</version>
              <executions>
                <execution>
                  <phase>verify</phase>
                  <goals>
                    <goal>sign</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
          </plugins>
      </build>
      <distributionManagement>
        <repository>
          <id>sonatype-nexus-staging</id>
          <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
      </distributionManagement>
    </profile>
    <!--私服-->
    <profile>
      <id>nexus</id>
      <build>
        <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-gpg-plugin</artifactId>
              <version>${maven-gpg-version}</version>
              <executions>
                <execution>
                  <phase>verify</phase>
                  <goals>
                    <goal>sign</goal>
                  </goals>
                </execution>
              </executions>
            </plugin>
          </plugins>
      </build>
      <distributionManagement>
        <repository>
          <id>xkcoding-nexus</id>
          <url>https://nexus.xkcoding.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
          <id>xkcoding-nexus</id>
          <url>https://nexus.xkcoding.com/repository/maven-snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
    <!-- 禁用 Javadoc 注释检查 -->
    <profile>
        <id>disable-javadoc-doclint</id>
        <activation>
            <jdk>[1.8,)</jdk>
        </activation>
        <properties>
            <javadoc.opts>-Xdoclint:none</javadoc.opts>
        </properties>
    </profile>
  </profiles>
</project>
