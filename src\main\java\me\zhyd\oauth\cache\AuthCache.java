package me.zhyd.oauth.cache;

/**
 * JustAuth缓存，用来缓存State
 *
 * <AUTHOR> (yadong.zhang0415(a)gmail.com)
 * @since 1.9.3
 */
public interface AuthCache {

    /**
     * 设置缓存
     *
     * @param key   缓存KEY
     * @param value 缓存内容
     */
    void set(String key, String value);

    /**
     * 设置缓存，指定过期时间
     *
     * @param key     缓存KEY
     * @param value   缓存内容
     * @param timeout 指定缓存过期时间（毫秒）
     */
    void set(String key, String value, long timeout);

    /**
     * 获取缓存
     *
     * @param key 缓存KEY
     * @return 缓存内容
     */
    String get(String key);

    /**
     * 是否存在key，如果对应key的value值已过期，也返回false
     *
     * @param key 缓存KEY
     * @return true：存在key，并且value没过期；false：key不存在或者已过期
     */
    boolean containsKey(String key);

    /**
     * 清理过期的缓存
     */
    default void pruneCache() {
    }

}
